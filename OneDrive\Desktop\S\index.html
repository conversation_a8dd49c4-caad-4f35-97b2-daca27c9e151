<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebX - Professional Networking & Freelance Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'webx-purple': '#8B5CF6',
                        'webx-blue': '#3B82F6',
                        'webx-dark': '#1E293B',
                    },
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'fadeInUp': 'fadeInUp 0.8s ease-out',
                        'slideInLeft': 'slideInLeft 0.8s ease-out',
                        'slideInRight': 'slideInRight 0.8s ease-out',
                    }
                }
            }
        }
    </script>
    <style>
        .hero-bg {
            background: linear-gradient(135deg, #FEFEFE 0%, #F8FAFC 50%, #F1F5F9 100%);
        }

        .gradient-text {
            background: linear-gradient(135deg, #8B5CF6, #3B82F6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #8B5CF6, #3B82F6);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .scroll-smooth {
            scroll-behavior: smooth;
        }
        
        .navbar-blur {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .testimonial-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .contact-form input, .contact-form textarea {
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        
        .contact-form input:focus, .contact-form textarea:focus {
            border-color: #8B5CF6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }

        /* Remove the complex floating animations and keep simple pulse */
        @keyframes pulse {
            0%, 100% { 
                transform: scale(1);
                opacity: 1;
            }
            50% { 
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        .animate-pulse {
            animation: pulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body class="font-sans scroll-smooth">
    <!-- Navigation -->
    <nav id="navbar" class="fixed top-0 w-full z-50 transition-all duration-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="text-2xl font-bold gradient-text">WebX</div>
                </div>
                
                <!-- Desktop Menu -->
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="#home" class="nav-link text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium transition-colors">Home</a>
                        <a href="#about" class="nav-link text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium transition-colors">About</a>
                        <a href="#services" class="nav-link text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium transition-colors">Services</a>
                        <a href="#testimonials" class="nav-link text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium transition-colors">Testimonials</a>
                        <a href="#contact" class="nav-link text-gray-700 hover:text-purple-600 px-3 py-2 text-sm font-medium transition-colors">Contact</a>
                    </div>
                </div>
                
                <!-- CTA Button -->
                <div class="hidden md:block">
                    <button class="gradient-bg text-white px-6 py-2 rounded-full hover:opacity-90 transition-opacity">
                        Get Started
                    </button>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-purple-600">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden navbar-blur">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="#home" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-700 hover:text-purple-600">Home</a>
                <a href="#about" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-700 hover:text-purple-600">About</a>
                <a href="#services" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-700 hover:text-purple-600">Services</a>
                <a href="#testimonials" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-700 hover:text-purple-600">Testimonials</a>
                <a href="#contact" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-700 hover:text-purple-600">Contact</a>
                <button class="gradient-bg text-white px-6 py-2 rounded-full mt-4 ml-3">Get Started</button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
<section id="home" class="bg-white min-h-screen flex items-center pt-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <!-- Left Content -->
            <div class="text-center lg:text-left space-y-8">
                <div class="space-y-6">
                    <h1 class="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                        Elevate Your <span class="gradient-text">Professional Network</span>
                    </h1>
                    <p class="text-xl text-gray-600 leading-relaxed max-w-2xl">
                        Connect with top-tier professionals and access premium freelance opportunities in one streamlined platform.
                    </p>
                </div>
                
                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                    <button class="gradient-bg text-white px-6 py-3 rounded-md hover:opacity-90 transition-all duration-300 font-medium shadow-md">
                        Join Now - It's Free
                    </button>
                    <button class="bg-white text-gray-700 px-6 py-3 rounded-md border border-gray-200 hover:border-purple-300 hover:text-purple-600 transition-all duration-300 font-medium shadow-md">
                        Explore Features
                    </button>
                </div>
                
                <!-- Trust Indicators - More Professional -->
                <div class="pt-8 border-t border-gray-100">
                    <p class="text-sm text-gray-500 mb-4">TRUSTED BY PROFESSIONALS AT</p>
                    <div class="flex flex-wrap items-center justify-center lg:justify-start gap-6">
                        <div class="text-gray-400 font-medium">Google</div>
                        <div class="text-gray-400 font-medium">Microsoft</div>
                        <div class="text-gray-400 font-medium">IBM</div>
                        <div class="text-gray-400 font-medium">Deloitte</div>
                    </div>
                </div>
            </div>
            
            <!-- Right Content - Professional Illustration -->
            <div class="hidden lg:flex items-center justify-center">
                <div class="relative w-full max-w-md">
                    <!-- Professional illustration with subtle animation -->
                    <div class="relative overflow-hidden rounded-xl shadow-2xl">
                        <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80" 
                             alt="Professionals networking" 
                             class="w-full h-auto object-cover transition-transform duration-500 hover:scale-105">
                        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/50 to-transparent"></div>
                        <div class="absolute bottom-0 left-0 p-8 text-white">
                            <h3 class="text-xl font-semibold">"WebX helped me grow my network by 300%"</h3>
                            <p class="text-sm opacity-90">- Sarah Johnson, Marketing Director</p>
                        </div>
                    </div>
                    
                    <!-- Floating elements - subtle and professional -->
                    <div class="absolute -top-6 -right-6 bg-white p-4 rounded-lg shadow-lg border border-gray-100 animate-pulse">
                        <div class="flex items-center">
                            <div class="w-10 h-10 gradient-bg rounded-full flex items-center justify-center text-white mr-3">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <div>
                                <div class="text-xs text-gray-500">New</div>
                                <div class="text-sm font-medium">Projects</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-lg border border-gray-100 animate-pulse" style="animation-delay: 0.5s;">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white mr-3">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div>
                                <div class="text-xs text-gray-500">Connections</div>
                                <div class="text-sm font-medium">+1,234</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-800 mb-4">About WebX</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Revolutionizing professional networking by bridging the gap between talent and opportunity
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="space-y-6">
                    <h3 class="text-3xl font-bold text-gray-800">Professionals and Opportunities Align</h3>
                    <p class="text-lg text-gray-600 leading-relaxed">
                        <span class="font-semibold text-purple-600">WebX</span> is the modern fusion of professional networking power and the flexibility of freelance platforms. Born from a vision to redefine professional connections, we bridge the gap between talented individuals and meaningful work—without limitations.
                    </p>
                    <p class="text-lg text-gray-600 leading-relaxed">
                        Our platform combines the best of both worlds: the professional networking capabilities you need with the freelance opportunities you want, all in one seamless experience.
                    </p>
                    
                    <div class="grid grid-cols-2 gap-6 mt-8">
                        <div class="text-center">
                            <div class="text-3xl font-bold gradient-text">10K+</div>
                            <div class="text-gray-600">Active Professionals</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold gradient-text">5K+</div>
                            <div class="text-gray-600">Projects Completed</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold gradient-text">98%</div>
                            <div class="text-gray-600">Success Rate</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold gradient-text">24/7</div>
                            <div class="text-gray-600">Support</div>
                        </div>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="space-y-4">
                            <div class="w-20 h-20 gradient-bg rounded-full flex items-center justify-center text-white text-2xl shadow-lg">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div class="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center text-white text-2xl shadow-lg">
                                <i class="fas fa-code"></i>
                            </div>
                        </div>
                        <div class="space-y-4 mt-8">
                            <div class="w-20 h-20 bg-purple-500 rounded-full flex items-center justify-center text-white text-2xl shadow-lg">
                                <i class="fas fa-design"></i>
                            </div>
                            <div class="w-20 h-20 gradient-bg rounded-full flex items-center justify-center text-white text-2xl shadow-lg">
                                <i class="fas fa-marketing"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-800 mb-4">Our Services</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Comprehensive solutions for professionals and businesses
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
                    <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-network-wired"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">Professional Networking</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Connect with industry leaders, expand your professional circle, and build meaningful business relationships.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
                    <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">Freelance Projects</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Discover high-quality freelance opportunities that match your skills and career goals.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
                    <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">Team Building</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Build and manage remote teams with talented professionals from around the world.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
                    <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">Career Growth</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Access resources, mentorship, and opportunities to accelerate your professional development.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
                    <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">Secure Payments</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Enjoy secure, fast, and reliable payment processing for all your freelance work.
                    </p>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow">
                    <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center text-white text-2xl mb-6">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-4">24/7 Support</h3>
                    <p class="text-gray-600 leading-relaxed">
                        Get round-the-clock support from our dedicated team whenever you need assistance.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section id="testimonials" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-800 mb-4">What Our Users Say</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Real stories from professionals who've transformed their careers with WebX
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="testimonial-card bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white font-bold mr-4">
                            JS
                        </div>
                        <div>
                            <h4 class="font-bold text-gray-800">John Smith</h4>
                            <p class="text-gray-600 text-sm">Software Developer</p>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                    <p class="text-gray-600 leading-relaxed">
                        "WebX completely transformed my freelance career. I've connected with amazing clients and built lasting professional relationships. The platform is intuitive and the support is exceptional."
                    </p>
                </div>
                
                <div class="testimonial-card bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white font-bold mr-4">
                            AM
                        </div>
                        <div>
                            <h4 class="font-bold text-gray-800">Anna Martinez</h4>
                            <p class="text-gray-600 text-sm">UX Designer</p>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                    <p class="text-gray-600 leading-relaxed">
                        "As a designer, finding quality projects was always challenging. WebX made it easy to showcase my work and connect with clients who truly value good design. Highly recommended!"
                    </p>
                </div>
                
                <div class="testimonial-card bg-white p-8 rounded-2xl shadow-lg border border-gray-100">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white font-bold mr-4">
                            RK
                        </div>
                        <div>
                            <h4 class="font-bold text-gray-800">Robert Kim</h4>
                            <p class="text-gray-600 text-sm">Marketing Consultant</p>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                        <i class="fas fa-star text-yellow-400"></i>
                    </div>
                    <p class="text-gray-600 leading-relaxed">
                        "The networking opportunities on WebX are unmatched. I've expanded my professional network significantly and landed several high-value consulting projects. It's a game-changer!"
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-800 mb-4">Get In Touch</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Ready to transform your professional journey? Let's connect and explore the possibilities.
                </p>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div class="space-y-8">
                    <div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-6">Contact Information</h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white mr-4">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-800">Address</p>
                                    <p class="text-gray-600">123 Business District, Tech City, TC 12345</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white mr-4">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-800">Phone</p>
                                    <p class="text-gray-600">+****************</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white mr-4">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div>
                                    <p class="font-semibold text-gray-800">Email</p>
                                    <p class="text-gray-600"><EMAIL></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-2xl font-bold text-gray-800 mb-6">Follow Us</h3>
                        <div class="flex space-x-4">
                            <a href="#" class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white hover:opacity-80 transition-opacity">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white hover:opacity-80 transition-opacity">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white hover:opacity-80 transition-opacity">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center text-white hover:opacity-80 transition-opacity">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white p-8 rounded-2xl shadow-lg">
                    <form class="contact-form space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                <input type="text" id="firstName" name="firstName" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" required>
                            </div>
                            <div>
                                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                <input type="text" id="lastName" name="lastName" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" required>
                            </div>
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" id="email" name="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" required>
                        </div>
                        
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                            <input type="text" id="subject" name="subject" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none" required>
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                            <textarea id="message" name="message" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none resize-none" required></textarea>
                        </div>
                        
                        <button type="submit" class="w-full gradient-bg text-white py-3 rounded-lg hover:opacity-90 transition-opacity font-semibold">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-webx-dark text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="space-y-4">
                    <div class="text-3xl font-bold gradient-text">WebX</div>
                    <p class="text-gray-300 leading-relaxed">
                        Connecting professionals, creating opportunities, and building the future of work together.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-300 hover:text-white transition-colors">Home</a></li>
                        <li><a href="#about" class="text-gray-300 hover:text-white transition-colors">About</a></li>
                        <li><a href="#services" class="text-gray-300 hover:text-white transition-colors">Services</a></li>
                        <li><a href="#testimonials" class="text-gray-300 hover:text-white transition-colors">Testimonials</a></li>
                        <li><a href="#contact" class="text-gray-300 hover:text-white transition-colors">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Services</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Professional Networking</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Freelance Projects</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Team Building</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">Career Growth</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">24/7 Support</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4">Newsletter</h3>
                    <p class="text-gray-300 mb-4">Stay updated with our latest news and offers.</p>
                    <form class="space-y-3">
                        <input type="email" placeholder="Enter your email" class="w-full px-4 py-2 bg-gray-700 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <button type="submit" class="w-full gradient-bg text-white py-2 rounded-lg hover:opacity-90 transition-opacity">
                            Subscribe
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-12 pt-8 text-center">
                <p class="text-gray-300">
                    &copy; 2024 WebX. All rights reserved. | 
                    <a href="#" class="hover:text-white transition-colors">Privacy Policy</a> | 
                    <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                </p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="fixed bottom-8 right-8 w-12 h-12 gradient-bg text-white rounded-full shadow-lg hover:opacity-90 transition-all duration-300 opacity-0 invisible">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });

        // Navbar scroll effect
        const navbar = document.getElementById('navbar');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 50) {
                navbar.classList.add('navbar-blur');
            } else {
                navbar.classList.remove('navbar-blur');
            }
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    // Close mobile menu if open
                    mobileMenu.classList.add('hidden');
                }
            });
        });

        // Back to top button
        const backToTopBtn = document.getElementById('backToTop');
        
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                backToTopBtn.classList.remove('opacity-0', 'invisible');
                backToTopBtn.classList.add('opacity-100', 'visible');
            } else {
                backToTopBtn.classList.add('opacity-0', 'invisible');
                backToTopBtn.classList.remove('opacity-100', 'visible');
            }
        });
        
        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Contact form submission
        document.querySelector('.contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Simulate form submission
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;
            
            setTimeout(() => {
                alert('Thank you for your message! We\'ll get back to you soon.');
                this.reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }, 2000);
        });

        // Newsletter subscription
        document.querySelector('footer form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if (email) {
                alert('Thank you for subscribing to our newsletter!');
                this.reset();
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fadeInUp');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.testimonial-card, .bg-white').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>